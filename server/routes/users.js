const express = require('express');
const router = express.Router();

const User = require('../models/UserPrisma');
const Listing = require('../models/ListingPrisma');
const { authenticateToken } = require('../middleware/auth');
const ResponseHelper = require('../utils/response');
const Validator = require('../utils/validator');

/**
 * 接口 2: 更新用户信息
 * PUT /api/v1/users/me
 * 
 * 输入: 用户昵称 nickname、头像 avatar_url
 * 逻辑: 使用 JWT 认证中间件，从 JWT 中解析出 user_id，并更新 users 表中对应的数据
 * 输出: 更新后的用户信息
 */
router.put('/me', authenticateToken, async (req, res) => {
  try {
    const { nickname, avatar_url, phone_number } = req.body;
    const userId = req.user.id;

    // 构建更新数据对象
    const updateData = {};
    if (nickname !== undefined) updateData.nickname = nickname;
    if (avatar_url !== undefined) updateData.avatar_url = avatar_url;
    if (phone_number !== undefined) updateData.phone_number = phone_number;

    // 验证数据
    const errors = Validator.validateUserData(updateData);
    if (errors.length > 0) {
      return ResponseHelper.validationError(res, errors);
    }

    // 更新用户信息
    const updatedUser = await User.update(userId, updateData);

    ResponseHelper.success(res, updatedUser.toJSON(), '用户信息更新成功');

  } catch (error) {
    console.error('更新用户信息错误:', error);
    ResponseHelper.serverError(res, '更新用户信息失败', error);
  }
});

/**
 * 接口 3: 获取当前用户信息
 * GET /api/v1/users/me
 * 
 * 逻辑: 使用 JWT 中间件，解析 user_id，查询并返回用户的完整信息
 */
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = req.user;

    // 获取用户的统计信息
    const userListings = await Listing.getUserListings(user.id, { page: 1, pageSize: 1 });
    const totalListings = userListings.pagination.total;

    const userInfo = {
      ...user.toJSON(),
      statistics: {
        total_listings: totalListings
      }
    };

    ResponseHelper.success(res, userInfo, '获取用户信息成功');

  } catch (error) {
    console.error('获取用户信息错误:', error);
    ResponseHelper.serverError(res, '获取用户信息失败', error);
  }
});

/**
 * 接口 7: 获取我发布的信息
 * GET /api/v1/users/me/listings
 * 
 * 逻辑: 使用 JWT 中间件，查询当前用户发布的所有信息，支持分页
 * 输出: 列表数据
 */
router.get('/me/listings', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 验证分页参数
    const { page, pageSize } = Validator.validatePagination(req.query);

    // 获取用户发布的信息列表
    const result = await Listing.getUserListings(userId, { page, pageSize });

    ResponseHelper.paginated(
      res,
      result.data.map(listing => listing.toJSON()),
      result.pagination,
      '获取我的发布成功'
    );

  } catch (error) {
    console.error('获取用户发布信息错误:', error);
    ResponseHelper.serverError(res, '获取我的发布失败', error);
  }
});

/**
 * 获取用户详情（管理员接口）
 * GET /api/v1/users/:id
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // 这里可以添加管理员权限检查
    // if (req.user.role !== 'admin') {
    //   return ResponseHelper.forbidden(res, '权限不足');
    // }

    const user = await User.findById(id);
    if (!user) {
      return ResponseHelper.notFound(res, '用户不存在');
    }

    // 获取用户的统计信息
    const userListings = await Listing.getUserListings(id, { page: 1, pageSize: 1 });
    const totalListings = userListings.pagination.total;

    const userInfo = {
      ...user.toJSON(),
      statistics: {
        total_listings: totalListings
      }
    };

    ResponseHelper.success(res, userInfo, '获取用户详情成功');

  } catch (error) {
    console.error('获取用户详情错误:', error);
    ResponseHelper.serverError(res, '获取用户详情失败', error);
  }
});

/**
 * 手动激活用户（管理员接口）
 * POST /api/v1/users/:id/activate
 */
router.post('/:id/activate', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // 这里可以添加管理员权限检查
    // if (req.user.role !== 'admin') {
    //   return ResponseHelper.forbidden(res, '权限不足');
    // }

    const user = await User.findById(id);
    if (!user) {
      return ResponseHelper.notFound(res, '用户不存在');
    }

    if (user.status === 'active') {
      return ResponseHelper.error(res, '用户已经是激活状态', 400);
    }

    const activatedUser = await User.activate(id);

    ResponseHelper.success(res, activatedUser.toJSON(), '用户激活成功');

  } catch (error) {
    console.error('激活用户错误:', error);
    ResponseHelper.serverError(res, '激活用户失败', error);
  }
});

/**
 * 增加用户发布额度（管理员接口）
 * POST /api/v1/users/:id/credits
 */
router.post('/:id/credits', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { credits } = req.body;

    // 这里可以添加管理员权限检查
    // if (req.user.role !== 'admin') {
    //   return ResponseHelper.forbidden(res, '权限不足');
    // }

    if (!credits || typeof credits !== 'number' || credits <= 0) {
      return ResponseHelper.validationError(res, ['credits 必须是正整数']);
    }

    const user = await User.findById(id);
    if (!user) {
      return ResponseHelper.notFound(res, '用户不存在');
    }

    const updatedUser = await User.addCredits(id, credits);

    ResponseHelper.success(res, updatedUser.toJSON(), `成功增加 ${credits} 个发布额度`);

  } catch (error) {
    console.error('增加用户额度错误:', error);
    ResponseHelper.serverError(res, '增加用户额度失败', error);
  }
});

/**
 * 获取用户统计信息
 * GET /api/v1/users/stats
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await User.getStats();
    ResponseHelper.success(res, stats, '获取用户统计成功');
  } catch (error) {
    console.error('获取用户统计错误:', error);
    ResponseHelper.serverError(res, '获取用户统计失败', error);
  }
});

module.exports = router;
