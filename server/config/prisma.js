const { PrismaClient } = require('@prisma/client');

/**
 * Prisma客户端实例
 * 使用单例模式确保整个应用只有一个Prisma客户端实例
 */
let prisma;

if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient();
} else {
  // 在开发环境中，使用全局变量避免热重载时创建多个实例
  if (!global.__prisma) {
    global.__prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'], // 开发环境显示SQL日志
    });
  }
  prisma = global.__prisma;
}

/**
 * 测试Prisma连接
 */
async function testPrismaConnection() {
  try {
    await prisma.$connect();
    console.log('✅ Prisma数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ Prisma数据库连接失败:', error.message);
    return false;
  }
}

/**
 * 优雅关闭Prisma连接
 */
async function disconnectPrisma() {
  await prisma.$disconnect();
}

module.exports = {
  prisma,
  testPrismaConnection,
  disconnectPrisma
};
